extends Control

# Nová sekcia "O hre" s krásnym Dark Templar dizajnom

@onready var main_panel: NinePatchRect = $MainPanel
@onready var title_label: Label = $MainPanel/ContentContainer/TitleContainer/TitleLabel
@onready var content_panel: NinePatchRect = $MainPanel/ContentContainer/ContentPanel
@onready var game_title: Label = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/GameTitleContainer/GameTitle
@onready var description: RichTextLabel = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/Description
@onready var privacy_policy_button: TextureButton = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/PolicyButtonsContainer/PrivacyPolicyButton
@onready var privacy_policy_label: Label = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/PolicyButtonsContainer/PrivacyPolicyButton/PrivacyPolicyLabel
@onready var terms_of_service_button: TextureButton = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/PolicyButtonsContainer/TermsOfServiceButton
@onready var terms_of_service_label: Label = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/PolicyButtonsContainer/TermsOfServiceButton/TermsOfServiceLabel
@onready var back_button: TextureButton = $MainPanel/ContentContainer/ButtonContainer/BackButton
@onready var back_label: Label = $MainPanel/ContentContainer/ButtonContainer/BackButton/BackLabel

func _ready():
	print("Nová AboutGame sekcia načítaná")
	
	# Pripojenie signálov
	setup_signals()
	
	# Aplikovanie fontov a štýlov
	apply_styling()
	
	# Nastavenie obsahu
	setup_content()
	
	# Nastavenie fokusu
	if back_button:
		back_button.grab_focus()

func setup_signals():
	"""Pripojí signály pre buttony"""
	if back_button:
		back_button.pressed.connect(_on_back_pressed)
	if privacy_policy_button:
		privacy_policy_button.pressed.connect(_on_privacy_policy_pressed)
	if terms_of_service_button:
		terms_of_service_button.pressed.connect(_on_terms_of_service_pressed)

func apply_styling():
	"""Aplikuje fonty a farby na všetky elementy"""

	if FontLoader:
		# Titulok sekcie
		if title_label:
			FontLoader.apply_font_style(title_label, "chapter_title")
			title_label.add_theme_color_override("font_color", Color("#D4AF37"))
			title_label.add_theme_font_size_override("font_size", 42)
			title_label.add_theme_constant_override("outline_size", 2)
			title_label.add_theme_color_override("font_outline_color", Color("#2A1810"))

		# Titulok hry
		if game_title:
			FontLoader.apply_font_style(game_title, "chapter_title")
			game_title.add_theme_color_override("font_color", Color("#D4AF37"))
			game_title.add_theme_font_size_override("font_size", 32)
			game_title.add_theme_constant_override("outline_size", 2)
			game_title.add_theme_color_override("font_outline_color", Color("#2A1810"))

		# Popis hry
		if description:
			FontLoader.apply_font_style(description, "character_dialogue")
			description.add_theme_color_override("default_color", Color("#F5F5DC"))
			description.add_theme_font_size_override("normal_font_size", 20)
			description.add_theme_color_override("font_outline_color", Color("#2A1810"))
			description.add_theme_constant_override("outline_size", 1)

		# Tlačidlo späť
		if back_label:
			FontLoader.apply_font_style(back_label, "ui_elements")
			back_label.add_theme_color_override("font_color", Color("#D4AF37"))
			back_label.add_theme_font_size_override("font_size", 28)
			back_label.add_theme_constant_override("outline_size", 2)
			back_label.add_theme_color_override("font_outline_color", Color("#2A1810"))

func setup_content():
	"""Nastaví obsah sekcie"""
	if title_label:
		title_label.text = "O HRE"
	
	if game_title:
		game_title.text = "VAN HELSING: PREKLIATE DEDIČSTVO"
	
	if description:
		description.text = """[center][b]Gotická hra inšpirovaná príbehmi Van Helsinga[/b][/center]

[b]Príbeh:[/b]
Vstúpte do sveta Van Helsinga a jeho učeníka Viktora. Keď Van Helsing záhadne zmizne v karpatských horách, Viktor sa vydáva na nebezpečnú cestu do temného zámku, aby odhalil pravdu o prekliatiach a zachránil svojho mentora.

[b]Hrateľnosť:[/b]
• 7 kapitol plných atmosféry a napätia
• Epilóg odhaľujúci záverečné tajomstvá  
• Logické hlavolamy a šifry
• Bohatý príbeh s hlasovými dialógmi
• Gotické prostredie Karpát

[b]Ovládanie:[/b]
• Kliknutie/Dotyky - Navigácia a výber
• Enter/Medzerník - Pokračovať v dialógu
• Escape - Návrat do menu

[center][i]Verzia 1.0.0[/i][/center]"""
	
	if back_label:
		back_label.text = "SPÄŤ"

func _on_back_pressed():
	"""Návrat do hlavného menu"""
	print("Návrat do hlavného menu")
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _on_privacy_policy_pressed():
	"""Otvorí Privacy Policy obrazovku"""
	print("Otváram Privacy Policy")
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/PrivacyPolicyScreen.tscn")

func _on_terms_of_service_pressed():
	"""Otvorí Terms of Service obrazovku"""
	print("Otváram Terms of Service")
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/TermsOfServiceScreen.tscn")

func _input(event):
	"""Spracovanie klávesových skratiek"""
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()

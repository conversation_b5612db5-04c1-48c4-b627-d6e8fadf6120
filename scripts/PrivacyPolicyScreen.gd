extends Control

# Privacy Policy obrazovka

@onready var title_label: Label = $MainPanel/ContentContainer/TitleContainer/TitleLabel
@onready var policy_text: RichTextLabel = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/PolicyText
@onready var back_button: TextureButton = $MainPanel/ContentContainer/ButtonContainer/BackButton
@onready var back_label: Label = $MainPanel/ContentContainer/ButtonContainer/BackButton/BackLabel

func _ready():
	print("Privacy Policy obrazovka načítaná")
	
	# Pripojenie signálov
	setup_signals()
	
	# Aplikovanie fontov a štýlov
	apply_styling()
	
	# Nastavenie obsahu
	setup_content()
	
	# Nastavenie fokusu
	if back_button:
		back_button.grab_focus()

func setup_signals():
	"""Pripojí signály pre buttony"""
	if back_button:
		back_button.pressed.connect(_on_back_pressed)

func apply_styling():
	"""Aplikuje fonty a farby na všetky elementy"""
	# Aplikovanie fontov cez FontLoader
	if FontLoader:
		if title_label:
			FontLoader.apply_font_style(title_label, "chapter_title")
			title_label.add_theme_color_override("font_color", Color("#D4AF37"))
			title_label.add_theme_font_size_override("font_size", 28)
		
		if policy_text:
			FontLoader.apply_font_style(policy_text, "dialogue")
			policy_text.add_theme_color_override("default_color", Color("#F5F5DC"))
			policy_text.add_theme_font_size_override("normal_font_size", 16)
		
		if back_label:
			FontLoader.apply_font_style(back_label, "ui")
			back_label.add_theme_color_override("font_color", Color("#F5F5DC"))
			back_label.add_theme_font_size_override("font_size", 18)

func setup_content():
	"""Nastaví obsah Privacy Policy"""
	if title_label:
		title_label.text = "OCHRANA OSOBNÝCH ÚDAJOV"
	
	if policy_text:
		policy_text.text = """[center][b]Ochrana osobných údajov (Privacy Policy)[/b][/center]

[b]Názov hry:[/b] Prekliate dedičstvo
[b]Tvorca:[/b] Vladimír Seman
[b]Dátum účinnosti:[/b] 27. 6. 2025

[b]1) Zber údajov[/b]

Táto hra nezhromažďuje žiadne osobné údaje hráčov. Neuchovávame meno, email, IP adresu ani iné informácie, ktoré by mohli viesť k identifikácii používateľa.

[b]2) Použitie údajov[/b]

Keďže hra nezhromažďuje žiadne údaje, nepoužívame ani nespracúvame žiadne osobné informácie.

[b]3) Zdieľanie údajov[/b]

Nezdieľame žiadne údaje s tretími stranami, pretože žiadne osobné údaje nezbierame.

[b]4) Bezpečnosť[/b]

Aj keď hra nepracuje s osobnými údajmi, snažíme sa zabezpečiť, aby hra neobsahovala bezpečnostné zraniteľnosti a aby bola stabilná a bezpečná pre používateľov.

[b]5) Práva používateľov[/b]

Vzhľadom na to, že neuchovávame žiadne osobné údaje, nie je potrebné uplatňovať právo na prístup, opravu alebo vymazanie údajov. Ak by ste mali otázky, môžete nás kontaktovať na uvedených kontaktoch.

[b]6) Kontakt[/b]

Ak máte akékoľvek otázky ohľadom ochrany osobných údajov alebo hry samotnej, kontaktujte nás na:
• Telefón: 0940 400 222
• Email: <EMAIL>

[b]7) Zmeny v dokumente[/b]

Vyhradzujeme si právo túto politiku ochrany osobných údajov kedykoľvek upraviť. Aktualizovaná verzia bude vždy súčasťou sekcie „O hre" v aplikácii."""
	
	if back_label:
		back_label.text = "SPÄŤ"

func _on_back_pressed():
	"""Návrat do sekcie O hre"""
	print("Návrat do sekcie O hre")
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/AboutGameNew.tscn")

func _input(event):
	"""Spracovanie klávesových skratiek"""
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()

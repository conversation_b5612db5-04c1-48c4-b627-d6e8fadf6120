extends Control

# Závereč<PERSON> obrazovka s poďakovaním

@onready var title_label: Label = $MainPanel/ContentContainer/TitleContainer/TitleLabel
@onready var game_completed_label: Label = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/GameCompletedLabel
@onready var credits_text: RichTextLabel = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/CreditsText
@onready var main_menu_button: TextureButton = $MainPanel/ContentContainer/ButtonContainer/MainMenuButton
@onready var main_menu_label: Label = $MainPanel/ContentContainer/ButtonContainer/MainMenuButton/MainMenuLabel

func _ready():
	print("Záverečná obrazovka načítaná")
	
	# Pripojenie signálov
	setup_signals()
	
	# Aplikovanie fontov a štýlov
	apply_styling()
	
	# Nastavenie obsahu
	setup_content()
	
	# Nastavenie fokusu
	if main_menu_button:
		main_menu_button.grab_focus()

func setup_signals():
	"""<PERSON><PERSON><PERSON><PERSON><PERSON> sign<PERSON><PERSON> pre buttony"""
	if main_menu_button:
		main_menu_button.pressed.connect(_on_main_menu_pressed)

func apply_styling():
	"""Aplikuje fonty a farby na všetky elementy"""
	# Aplikovanie fontov cez FontLoader
	if FontLoader:
		if title_label:
			FontLoader.apply_font_style(title_label, "chapter_title")
			title_label.add_theme_color_override("font_color", Color("#D4AF37"))
			title_label.add_theme_font_size_override("font_size", 32)
		
		if game_completed_label:
			FontLoader.apply_font_style(game_completed_label, "dialogue")
			game_completed_label.add_theme_color_override("font_color", Color("#F5F5DC"))
			game_completed_label.add_theme_font_size_override("font_size", 20)
		
		if credits_text:
			FontLoader.apply_font_style(credits_text, "dialogue")
			credits_text.add_theme_color_override("default_color", Color("#F5F5DC"))
			credits_text.add_theme_font_size_override("normal_font_size", 16)
		
		if main_menu_label:
			FontLoader.apply_font_style(main_menu_label, "ui")
			main_menu_label.add_theme_color_override("font_color", Color("#F5F5DC"))
			main_menu_label.add_theme_font_size_override("font_size", 18)

func setup_content():
	"""Nastaví obsah záverečnej obrazovky"""
	if title_label:
		title_label.text = "ĎAKUJEME ZA HRANIE!"
	
	if game_completed_label:
		game_completed_label.text = "Úspešne ste dokončili hru Van Helsing: Prekliate Dedičstvo!"
	
	if credits_text:
		credits_text.text = """[center][b]Tvorca hry[/b][/center]
[center]Vladimír Seman[/center]
[center]0940 400 222[/center]
[center]<EMAIL>[/center]

[center][b]Špeciálne poďakovanie[/b][/center]
[center]Escape Land Žilina[/center]
[center]www.escapeland.sk[/center]

[center][i]Ďakujeme, že ste sa vydali na túto gotickú cestu
plnú záhad a tajomstiev v Karpatských horách.
Dúfame, že ste si užili príbeh Van Helsinga
a jeho učeníka Viktora.[/i][/center]

[center][b]Verzia 1.0.0[/b][/center]"""
	
	if main_menu_label:
		main_menu_label.text = "HLAVNÉ MENU"

func _on_main_menu_pressed():
	"""Návrat do hlavného menu"""
	print("Návrat do hlavného menu z záverečnej obrazovky")
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _input(event):
	"""Spracovanie klávesových skratiek"""
	if event.is_action_pressed("ui_accept") or event.is_action_pressed("ui_cancel"):
		_on_main_menu_pressed()

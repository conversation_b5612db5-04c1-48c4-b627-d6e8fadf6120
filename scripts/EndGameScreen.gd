extends Control

# Záverečná obrazovka s poďakovaním

@onready var title_label: Label = $MainPanel/ContentContainer/TitleContainer/TitleLabel
@onready var game_completed_label: Label = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/GameCompletedLabel
@onready var credits_text: RichTextLabel = $MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/CreditsText
@onready var main_menu_button: TextureButton = $MainPanel/ContentContainer/ButtonContainer/MainMenuButton
@onready var main_menu_label: Label = $MainPanel/ContentContainer/ButtonContainer/MainMenuButton/MainMenuLabel

func _ready():
	print("🎉 DEBUG: Záverečná obrazovka _ready() začína")

	# Pripojenie signálov
	setup_signals()
	print("🎉 DEBUG: Signály pripojené")

	# Aplikovanie fontov a štýlov
	apply_styling()
	print("🎉 DEBUG: Štýly aplikované")

	# Nastavenie obsahu
	setup_content()
	print("🎉 DEBUG: Obsah nastavený")

	# Nastavenie fokusu
	if main_menu_button:
		main_menu_button.grab_focus()
		print("🎉 DEBUG: Fokus nastavený na button")

	print("🎉 DEBUG: Záverečná obrazovka úspešne načítaná a zobrazená!")

	# Nechať hrať hudbu z kapitoly 7 (nezmeniť hudbu)
	print("🎉 DEBUG: Ponechávam hudbu z kapitoly 7")

func setup_signals():
	"""Pripojí signály pre buttony"""
	if main_menu_button:
		main_menu_button.pressed.connect(_on_main_menu_pressed)

func apply_styling():
	"""Aplikuje fonty a farby na všetky elementy"""
	# Aplikovanie fontov cez FontLoader
	if FontLoader:
		if title_label:
			FontLoader.apply_font_style(title_label, "chapter_title")
			title_label.add_theme_color_override("font_color", Color("#D4AF37"))
			title_label.add_theme_font_size_override("font_size", 32)
		
		if game_completed_label:
			FontLoader.apply_font_style(game_completed_label, "dialogue")
			game_completed_label.add_theme_color_override("font_color", Color("#F5F5DC"))
			game_completed_label.add_theme_font_size_override("font_size", 20)
		
		if credits_text:
			FontLoader.apply_font_style(credits_text, "dialogue")
			credits_text.add_theme_color_override("default_color", Color("#F5F5DC"))
			credits_text.add_theme_font_size_override("normal_font_size", 16)
		
		if main_menu_label:
			FontLoader.apply_font_style(main_menu_label, "ui")
			main_menu_label.add_theme_color_override("font_color", Color("#F5F5DC"))
			main_menu_label.add_theme_font_size_override("font_size", 18)

func setup_content():
	"""Nastaví obsah záverečnej obrazovky"""
	if title_label:
		title_label.text = "VAN HELSING: PREKLIATE DEDIČSTVO"

	if game_completed_label:
		game_completed_label.text = "Poďakovanie"

	if credits_text:
		credits_text.text = """[center][b]Tvorca hry[/b][/center]
[center]Vladimír Seman[/center]
[center]0940 400 222[/center]
[center]<EMAIL>[/center]

[center][b]Špeciálne poďakovanie[/b][/center]
[center]Escape Land Žilina[/center]
[center]www.escapeland.sk[/center]"""

	if main_menu_label:
		main_menu_label.text = "HLAVNÉ MENU"

func _on_main_menu_pressed():
	"""Návrat do hlavného menu"""
	print("🎉 DEBUG: _on_main_menu_pressed() volaná - používateľ klikol na button")
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _input(event):
	"""Spracovanie klávesových skratiek"""
	if event.is_action_pressed("ui_accept") or event.is_action_pressed("ui_cancel"):
		print("🎉 DEBUG: _input() zachytil klávesovú skratku - automatický návrat do menu")
		_on_main_menu_pressed()

[res://scripts/NewChaptersMenu.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 105,
"scroll_position": 89.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/DialogueSystem.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 1117,
"scroll_position": 1101.0,
"selection": true,
"selection_from_column": 0,
"selection_from_line": 1110,
"selection_to_column": 0,
"selection_to_line": 1117,
"syntax_highlighter": "GDScript"
}

[res://scripts/VampireArithmeticPuzzle.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 12,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Chapter.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 191,
"scroll_position": 191.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
